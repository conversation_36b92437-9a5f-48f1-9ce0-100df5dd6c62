{"scripts": {"build": "pnpm --filter @supabase/mcp-utils --filter @supabase/mcp-server-supabase build", "test": "pnpm --parallel --filter @supabase/mcp-utils --filter @supabase/mcp-server-supabase test", "test:coverage": "pnpm --filter @supabase/mcp-server-supabase test:coverage", "format": "biome check --write .", "format:check": "biome check ."}, "devDependencies": {"@biomejs/biome": "1.9.4", "supabase": "^2.1.1"}, "packageManager": "pnpm@10.15.0"}