name: Tests
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    env:
      ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          run_install: false
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install --ignore-scripts
      - name: Build libs
        run: |
          pnpm run build
          pnpm rebuild # To create bin links
      - name: Tests
        run: pnpm run test:coverage
      - name: Upload coverage results to Coveralls
        uses: coverallsapp/github-action@v2
        with:
          base-path: ./packages/mcp-server-supabase
