{"name": "@supabase/mcp-server-supabase", "version": "0.5.2", "description": "MCP server for interacting with Supabase", "license": "Apache-2.0", "type": "module", "main": "dist/index.cjs", "types": "dist/index.d.ts", "sideEffects": false, "scripts": {"build": "tsup --clean", "dev": "tsup --watch", "typecheck": "tsc --noEmit", "prebuild": "pnpm typecheck", "prepublishOnly": "pnpm build", "test": "vitest", "test:unit": "vitest --project unit", "test:e2e": "vitest --project e2e", "test:integration": "vitest --project integration", "test:coverage": "vitest --coverage", "generate:management-api-types": "openapi-typescript https://api.supabase.com/api/v1-json -o ./src/management-api/types.ts"}, "files": ["dist/**/*"], "bin": {"mcp-server-supabase": "./dist/transports/stdio.js"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.cjs"}, "./platform": {"types": "./dist/platform/index.d.ts", "import": "./dist/platform/index.js", "default": "./dist/platform/index.cjs"}, "./platform/api": {"types": "./dist/platform/api-platform.d.ts", "import": "./dist/platform/api-platform.js", "default": "./dist/platform/api-platform.cjs"}}, "dependencies": {"@mjackson/multipart-parser": "^0.10.1", "@modelcontextprotocol/sdk": "^1.11.0", "@supabase/mcp-utils": "workspace:^", "common-tags": "^1.8.2", "graphql": "^16.11.0", "openapi-fetch": "^0.13.5", "zod": "^3.24.1"}, "devDependencies": {"@ai-sdk/anthropic": "^1.2.9", "@electric-sql/pglite": "^0.2.17", "@total-typescript/tsconfig": "^1.0.4", "@types/common-tags": "^1.8.4", "@types/node": "^22.8.6", "@vitest/coverage-v8": "^2.1.9", "ai": "^4.3.4", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "msw": "^2.7.3", "nanoid": "^5.1.5", "openapi-typescript": "^7.5.0", "openapi-typescript-helpers": "^0.0.15", "prettier": "^3.3.3", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "^5.6.3", "vite": "^5.4.19", "vitest": "^2.1.9"}}